"""
Rate limiting middleware for the Agentic RAG system using slowapi.
"""

import logging
from typing import Dict, Optional
from fastapi import Request, HTTPException
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware

from app.core.agentic_rag import AgenticRAG
from app.core.logging_helpers import get_logger

logger = get_logger(__name__)


def get_session_id_from_request(request: Request) -> str:
    """
    Extract session_id from request for rate limiting.
    Falls back to remote address if no session_id is provided.
    """
    # Try to get session_id from request body (for POST requests)
    if hasattr(request.state, "session_id") and request.state.session_id:
        return request.state.session_id

    # Try to get session_id from query parameters
    session_id = request.query_params.get("session_id")
    if session_id:
        return session_id

    # Fall back to remote address
    return get_remote_address(request)


def get_bot_specific_key(request: Request) -> str:
    """
    Create a bot-specific key for rate limiting that includes both session_id and bot_name.
    """
    session_id = get_session_id_from_request(request)
    bot_name = getattr(request.state, "bot_name", "unknown")
    return f"{bot_name}:{session_id}"


def create_rate_limiter() -> Limiter:
    """Create and configure the rate limiter."""
    limiter = Limiter(
        key_func=get_session_id_from_request,
        default_limits=["100/hour", "10/minute"],  # Default limits
    )
    return limiter


def get_bot_rate_limits(bot_name: str, agentic_rag: AgenticRAG) -> Dict[str, str]:
    """
    Get rate limits for a specific bot from its configuration.

    Args:
        bot_name: Name of the bot
        agentic_rag: AgenticRAG instance to get bot config

    Returns:
        Dictionary with rate limit strings for slowapi
    """
    try:
        bot = agentic_rag.get_bot(bot_name)
        if not bot:
            logger.warning(f"Bot not found: {bot_name}, using default rate limits")
            return {"minute": "10/minute", "hour": "100/hour"}

        config = bot["config"]
        rate_limit_config = getattr(config, "rate_limit", None)

        if not rate_limit_config or not rate_limit_config.enabled:
            logger.info(f"Rate limiting disabled for bot: {bot_name}")
            return {
                "minute": "1000/minute",
                "hour": "10000/hour",
            }  # Very high limits = disabled

        minute_limit = f"{rate_limit_config.requests_per_minute}/minute"
        hour_limit = f"{rate_limit_config.requests_per_hour}/hour"

        logger.info(f"Rate limits for {bot_name}: {minute_limit}, {hour_limit}")
        return {"minute": minute_limit, "hour": hour_limit}

    except Exception as e:
        logger.error(f"Error getting rate limits for bot {bot_name}: {str(e)}")
        return {"minute": "10/minute", "hour": "100/hour"}


def get_bot_rate_limit_decorator(bot_name: str, agentic_rag: AgenticRAG):
    """
    Get a rate limit decorator for a specific bot.

    Args:
        bot_name: Name of the bot
        agentic_rag: AgenticRAG instance to get bot config

    Returns:
        Rate limit decorator function
    """
    rate_limits = get_bot_rate_limits(bot_name, agentic_rag)

    # Create a limiter instance for this specific bot
    limiter = Limiter(
        key_func=get_session_id_from_request,
        default_limits=[rate_limits["hour"], rate_limits["minute"]],
    )

    # Return the limit decorator with the specific limits
    return limiter.limit(f"{rate_limits['minute']};{rate_limits['hour']}")


async def extract_session_id_middleware(request: Request, call_next):
    """
    Middleware to extract session_id from request body and store it in request.state
    for use by the rate limiter.
    """
    # Only process POST requests to bot query endpoints
    if (
        request.method == "POST"
        and "/bots/" in request.url.path
        and "/query" in request.url.path
    ):
        try:
            # Read the request body
            body = await request.body()
            if body:
                import json

                try:
                    body_data = json.loads(body.decode())
                    session_id = body_data.get("session_id")
                    if session_id:
                        request.state.session_id = session_id
                        logger.debug(f"Extracted session_id: {session_id}")

                    # Also extract bot name for rate limiting
                    path_parts = request.url.path.split("/")
                    for i, part in enumerate(path_parts):
                        if part == "bots" and i + 1 < len(path_parts):
                            request.state.bot_name = path_parts[i + 1]
                            break

                except json.JSONDecodeError:
                    logger.warning("Failed to parse request body as JSON")
                except Exception as e:
                    logger.warning(f"Error extracting session_id: {str(e)}")

            # Recreate the request with the original body
            async def receive():
                return {"type": "http.request", "body": body}

            request._receive = receive

        except Exception as e:
            logger.error(f"Error in session_id extraction middleware: {str(e)}")

    response = await call_next(request)
    return response


def rate_limit_exceeded_handler(request: Request, exc: RateLimitExceeded):
    """
    Custom handler for rate limit exceeded errors.
    """
    session_id = getattr(request.state, "session_id", "unknown")
    bot_name = request.path_params.get("bot_name", "unknown")

    logger.warning(
        f"Rate limit exceeded for session {session_id} on bot {bot_name}: {exc.detail}"
    )

    return HTTPException(
        status_code=429,
        detail={
            "error": "Rate limit exceeded",
            "message": f"Too many requests. Please wait before making another request.",
            "retry_after": exc.retry_after,
            "bot_name": bot_name,
            "session_id": session_id,
        },
    )
