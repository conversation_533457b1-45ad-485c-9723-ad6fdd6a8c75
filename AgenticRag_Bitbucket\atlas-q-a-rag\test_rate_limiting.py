#!/usr/bin/env python3
"""
Test script for rate limiting functionality.
"""

import asyncio
import aiohttp
import json
import time
from typing import List, Dict, Any


async def make_request(session: aiohttp.ClientSession, url: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Make a single request to the API."""
    try:
        async with session.post(url, json=data) as response:
            result = {
                "status_code": response.status,
                "response": await response.json(),
                "timestamp": time.time()
            }
            return result
    except Exception as e:
        return {
            "status_code": -1,
            "error": str(e),
            "timestamp": time.time()
        }


async def test_rate_limiting(bot_name: str, session_id: str, num_requests: int = 15, delay: float = 1.0):
    """
    Test rate limiting for a specific bot and session.
    
    Args:
        bot_name: Name of the bot to test
        session_id: Session ID to use for testing
        num_requests: Number of requests to make
        delay: Delay between requests in seconds
    """
    base_url = "http://localhost:8000"
    url = f"{base_url}/bots/{bot_name}/query"
    
    request_data = {
        "query": "Test query for rate limiting",
        "session_id": session_id,
        "metadata": {"test": True}
    }
    
    print(f"Testing rate limiting for bot: {bot_name}")
    print(f"Session ID: {session_id}")
    print(f"Making {num_requests} requests with {delay}s delay between requests")
    print("-" * 60)
    
    results = []
    
    async with aiohttp.ClientSession() as session:
        for i in range(num_requests):
            print(f"Request {i+1}/{num_requests}...", end=" ")
            
            result = await make_request(session, url, request_data)
            results.append(result)
            
            if result["status_code"] == 200:
                print("✅ SUCCESS")
            elif result["status_code"] == 429:
                print("🚫 RATE LIMITED")
                if "response" in result and "detail" in result["response"]:
                    detail = result["response"]["detail"]
                    if isinstance(detail, dict) and "retry_after" in detail:
                        print(f"   Retry after: {detail['retry_after']} seconds")
            else:
                print(f"❌ ERROR ({result['status_code']})")
                if "error" in result:
                    print(f"   Error: {result['error']}")
            
            # Wait before next request (except for the last one)
            if i < num_requests - 1:
                await asyncio.sleep(delay)
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r["status_code"] == 200)
    rate_limited_count = sum(1 for r in results if r["status_code"] == 429)
    error_count = sum(1 for r in results if r["status_code"] not in [200, 429])
    
    print(f"Total requests: {num_requests}")
    print(f"Successful: {success_count}")
    print(f"Rate limited: {rate_limited_count}")
    print(f"Errors: {error_count}")
    
    if rate_limited_count > 0:
        first_rate_limit = next((i for i, r in enumerate(results) if r["status_code"] == 429), None)
        if first_rate_limit is not None:
            print(f"First rate limit at request: {first_rate_limit + 1}")
    
    return results


async def test_different_sessions(bot_name: str, num_sessions: int = 3, requests_per_session: int = 8):
    """
    Test rate limiting with different session IDs to ensure isolation.
    """
    print(f"Testing rate limiting isolation with {num_sessions} different sessions")
    print(f"Each session will make {requests_per_session} requests")
    print("-" * 60)
    
    tasks = []
    for i in range(num_sessions):
        session_id = f"test_session_{i+1}"
        task = test_rate_limiting(bot_name, session_id, requests_per_session, delay=0.1)
        tasks.append(task)
    
    # Run all sessions concurrently
    results = await asyncio.gather(*tasks)
    
    print("\n" + "=" * 60)
    print("MULTI-SESSION SUMMARY")
    print("=" * 60)
    
    for i, session_results in enumerate(results):
        session_id = f"test_session_{i+1}"
        success_count = sum(1 for r in session_results if r["status_code"] == 200)
        rate_limited_count = sum(1 for r in session_results if r["status_code"] == 429)
        
        print(f"Session {session_id}: {success_count} success, {rate_limited_count} rate limited")


async def main():
    """Main test function."""
    print("Rate Limiting Test Script")
    print("=" * 60)
    
    # Test 1: Single session with StudentBot (10 requests/minute limit)
    print("\nTest 1: StudentBot - Single Session")
    await test_rate_limiting("StudentBot", "test_session_single", num_requests=12, delay=2.0)
    
    # Wait a bit between tests
    print("\nWaiting 10 seconds before next test...")
    await asyncio.sleep(10)
    
    # Test 2: Multiple sessions with StudentBot
    print("\nTest 2: StudentBot - Multiple Sessions")
    await test_different_sessions("StudentBot", num_sessions=3, requests_per_session=6)
    
    # Wait a bit between tests
    print("\nWaiting 10 seconds before next test...")
    await asyncio.sleep(10)
    
    # Test 3: AtlasIQBot (15 requests/minute limit)
    print("\nTest 3: AtlasIQBot - Single Session")
    await test_rate_limiting("AtlasIQBot", "test_session_atlasiq", num_requests=18, delay=2.0)


if __name__ == "__main__":
    print("Starting rate limiting tests...")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
